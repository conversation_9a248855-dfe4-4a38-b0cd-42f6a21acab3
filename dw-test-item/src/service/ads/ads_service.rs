use crate::config::DwTestItemConfig;
use crate::cpu_limit::get_current_thread_count;
use ck_provider::{AsyncCkChannel, AsyncCkSender, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig};
use clickhouse::Row;
use serde::Serialize;
use std::error::Error;
use std::time::Duration;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use common::ck::ck_sink::SinkHandler;

pub async fn write_to_clickhouse_concurrent<T: Sync + Send + Serialize + Row + 'static>(
    handler: &(impl SinkHandler + Send + Sync),
    properties: DwTestItemConfig,
) -> Result<(AsyncCkSender<T>, Join<PERSON><PERSON>le<()>), Box<dyn Error + Send + Sync>> {
    let ck_config = properties.get_ck_config(properties.dwd_db_name.as_str());

    let db_table_name = format!("{}.{}", handler.db_name(), handler.table_name());
    let batch_size = properties.get_batch_size()?;

    let optimal_concurrent_flushes = get_current_thread_count();
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<T>(stream_config.clone(), 10);

    let ck_provider = CkProviderImpl::new(ck_config.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // processor_handle.await?;
    Ok((sender, processor_handle))
}
