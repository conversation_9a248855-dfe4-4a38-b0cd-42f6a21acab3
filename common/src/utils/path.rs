use rand::seq::SliceRandom;

use crate::model::constant::*;
use std::fs::File;
use std::io::{BufRead, BufReader};

pub fn select_mount_point(prefix: Option<&str>) -> Result<String, Box<dyn std::error::Error>> {
    // 读取 /proc/mounts 文件获取系统挂载点信息
    let file = File::open("/proc/mounts")?;
    let reader = BufReader::new(file);
    
    let mut mount_points = Vec::new();
    
    // 解析挂载点信息
    for line in reader.lines() {
        let line = line?;
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() >= 2 {
            let mount_point = parts[1];
            // 过滤掉一些系统挂载点
            if !mount_point.starts_with("/proc") 
                && !mount_point.starts_with("/sys") 
                && !mount_point.starts_with("/dev") 
                && !mount_point.starts_with("/run")
                && mount_point != "/boot"
                && mount_point != "/boot/efi" {
                mount_points.push(mount_point.to_string());
            }
        }
    }
    
    // 根据前缀过滤挂载点
    let filtered_points: Vec<String> = match prefix {
        Some(p) if !p.is_empty() && p != "/" => {
            mount_points.into_iter()
                .filter(|mp| mp.starts_with(p))
                .collect()
        },
        _ => vec!["/tmp".to_string()], // 如果没有前缀或前缀为"/"，使用/tmp
    };
    
    // 如果没有找到匹配的挂载点，使用 /tmp 作为默认值
    let final_points = if filtered_points.is_empty() {
        vec!["/tmp".to_string()]
    } else {
        filtered_points
    };
    
    // 随机选择一个挂载点
    let mut rng = rand::thread_rng();
    let selected = final_points.choose(&mut rng)
        .ok_or("无法选择挂载点")?;
    
    Ok(selected.clone())
}

/// 获取ods lot的路径
pub fn get_ods_lot_path(
    base_path: &str,
    file_category: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    type_: &str,
    lot_id: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(FILE_CATEGORY, file_category)
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(TYPE, type_)
        .replace(LOT_ID, lot_id)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

/// 获取ods wafer的存储路径
pub fn get_ods_wafer_path(
    base_path: &str,
    file_category: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    type_: &str,
    lot_id: &str,
    wafer_no: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(FILE_CATEGORY, file_category)
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(TYPE, type_)
        .replace(LOT_ID, lot_id)
        .replace(WAFER_NO, wafer_no)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

/// 获取ods manual的路径
pub fn get_ods_manual_path(
    base_path: &str,
    type_: &str,
    file_category: &str,
    test_area: &str,
    customer: &str,
    file_id: &str,
) -> String {
    base_path
        .replace(FILE_CATEGORY, file_category)
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(TYPE, type_)
        .replace(FILE_ID, file_id)
}

/// 获取dwd lot的路径
pub fn get_dwd_lot_path(
    base_path: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

/// 获取dwd wafer的存储路径
pub fn get_dwd_wafer_path(
    base_path: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
    wafer_no: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
        .replace(WAFER_NO, wafer_no)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

/// 获取dwd history lot的路径
pub fn get_dwd_history_lot_path(
    base_path: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
) -> String {
    base_path
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
}

/// 获取dwd history wafer的存储路径
pub fn get_dwd_history_wafer_path(
    base_path: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
    wafer_no: &str,
) -> String {
    base_path
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
        .replace(WAFER_NO, wafer_no)
}

/// 获取lot的路径
pub fn get_lot_path(
    base_path: &str,
    table: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(TABLE, table)
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

/// 获取wafer的存储路径
pub fn get_wafer_path(
    base_path: &str,
    table: &str,
    test_area: &str,
    customer: &str,
    factory: &str,
    lot_id: &str,
    wafer_no: &str,
    device_id: &str,
    test_stage: &str,
    lot_type: &str,
) -> String {
    base_path
        .replace(TABLE, table)
        .replace(TEST_AREA, test_area)
        .replace(CUSTOMER, customer)
        .replace(FACTORY, factory)
        .replace(LOT_ID, lot_id)
        .replace(WAFER_NO, wafer_no)
        .replace(DEVICE_ID, device_id)
        .replace(TEST_STAGE, test_stage)
        .replace(LOT_TYPE, lot_type)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_ods_lot_path() {
        let base_path = "/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result = get_ods_lot_path(
            base_path,
            "stdf",
            "CP",
            "GUWAVE",
            "Fab3",
            "TEST_ITEM_DATA",
            "LOT123",
            "DEVICE456",
            "CP1",
            "PROD",
        );
        assert_eq!(result, "/user/glory/data/onedata/dataware/ods/result/stdf/TEST_ITEM_DATA/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }

    #[test]
    fn test_get_ods_wafer_path() {
        let base_path = "/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result = get_ods_wafer_path(
            base_path,
            "stdf",
            "CP",
            "GUWAVE",
            "Fab3",
            "TEST_ITEM_DATA",
            "LOT123",
            "W01",
            "DEVICE456",
            "CP1",
            "PROD",
        );
        assert_eq!(result, "/user/glory/data/onedata/dataware/ods/result/stdf/TEST_ITEM_DATA/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/WAFER_NO=W01/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }

    #[test]
    fn test_get_ods_manual_path() {
        let base_path = "/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FILE_ID={FILE_ID}";
        let result = get_ods_manual_path(base_path, "TEST_ITEM_DATA", "stdf", "CP", "GUWAVE", "FILE123");
        assert_eq!(result, "/user/glory/data/onedata/dataware/ods/result/stdf/TEST_ITEM_DATA/TEST_AREA=CP/CUSTOMER=GUWAVE/FILE_ID=FILE123");
    }

    #[test]
    fn test_get_dwd_lot_path() {
        let base_path = "/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result = get_dwd_lot_path(base_path, "CP", "GUWAVE", "Fab3", "LOT123", "DEVICE456", "CP1", "PROD");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }

    #[test]
    fn test_get_dwd_wafer_path() {
        let base_path =
            "/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result = get_dwd_wafer_path(base_path, "CP", "GUWAVE", "Fab3", "LOT123", "W01", "DEVICE456", "CP1", "PROD");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dwd/result/test_item_detail/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/WAFER_NO=W01/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }

    #[test]
    fn test_get_dwd_history_lot_path() {
        let base_path = "/user/glory/data/onedata/dataware/dwd_history/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/LOT_ID={LOT_ID}";
        let result = get_dwd_history_lot_path(base_path, "CP", "GUWAVE", "Fab3", "LOT123");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dwd_history/result/test_item_detail/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/LOT_ID=LOT123");
    }

    #[test]
    fn test_get_dwd_history_wafer_path() {
        let base_path = "/user/glory/data/onedata/dataware/dwd_history/result/test_item_detail/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}";
        let result = get_dwd_history_wafer_path(base_path, "CP", "GUWAVE", "Fab3", "LOT123", "W01");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dwd_history/result/test_item_detail/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/LOT_ID=LOT123/WAFER_NO=W01");
    }

    #[test]
    fn test_get_lot_path() {
        let base_path =
            "/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result = get_lot_path(base_path, "test_item", "CP", "GUWAVE", "Fab3", "LOT123", "DEVICE456", "CP1", "PROD");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dim/result/test_item/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }

    #[test]
    fn test_get_wafer_path() {
        let base_path =
            "/user/glory/data/onedata/dataware/dim/result/{TABLE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FACTORY={FACTORY}/DEVICE_ID={DEVICE_ID}/LOT_ID={LOT_ID}/WAFER_NO={WAFER_NO}/TEST_STAGE={TEST_STAGE}/LOT_TYPE={LOT_TYPE}";
        let result =
            get_wafer_path(base_path, "test_item", "CP", "GUWAVE", "Fab3", "LOT123", "W01", "DEVICE456", "CP1", "PROD");
        assert_eq!(result, "/user/glory/data/onedata/dataware/dim/result/test_item/TEST_AREA=CP/CUSTOMER=GUWAVE/FACTORY=Fab3/DEVICE_ID=DEVICE456/LOT_ID=LOT123/WAFER_NO=W01/TEST_STAGE=CP1/LOT_TYPE=PROD");
    }
}
