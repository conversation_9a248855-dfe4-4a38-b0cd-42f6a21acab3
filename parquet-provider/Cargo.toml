[package]
name = "parquet-provider"
version = "0.1.0"
edition = "2021"

[dependencies]
log = { workspace = true }
arrow = { workspace = true }
parquet = { workspace = true }
chrono = { workspace = true }
serde_arrow = { workspace = true }
common = { path = "../common" }
ck-provider = { path = "../ck-provider" }
env_logger = { workspace = true }
hdfs-native = "=0.11.0"
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"
uuid = { workspace = true, features = ["v4"] }
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bytes = "1.10.1"
color-eyre = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
opendal = { version = "0.54.0", features = ["services-hdfs"] }
walkdir = "2.4.0"
scopeguard = "1.1"
rayon = { workspace = true }
